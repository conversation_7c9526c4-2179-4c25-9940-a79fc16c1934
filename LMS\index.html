<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Academia — Premium LMS for Career Growth</title>
  <meta name="description" content="Expert-led courses, live sessions, verified certificates. Start your free trial today." />
  <meta property="og:title" content="Academia — Premium LMS" />
  <meta property="og:description" content="Expert-led courses, live sessions, verified certificates." />
  <meta property="og:type" content="website" />
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800&family=Merriweather:wght@300;400;700&display=swap" rel="stylesheet">
  <!-- Tailwind Play CDN - great for prototypes -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#0f5132',
            accent: '#e6ff8f',
            navy: '#0b2545'
          },
          fontFamily: {
            sans: ['Inter', 'ui-sans-serif', 'system-ui'],
            serif: ['Merriweather', 'serif']
          }
        }
      }
    }
  </script>
  <style>
    /* Custom styles for repeated groups, small post-Tailwind tweaks */
    :root{--container:1200px}
    .glass{background:rgba(255,255,255,0.04);backdrop-filter: blur(6px)}
    .focus-ring:focus{outline:2px solid rgba(99,102,241,0.18);outline-offset:2px}
    /* Pricing number animation */
    .num-anim{transition:all .35s cubic-bezier(.2,.9,.2,1)}
<!-- Service worker registration removed - this was incorrectly registering a moment.js locale file -->
  </style>
</head>
<body class="antialiased bg-gradient-to-b from-gray-50 to-white text-slate-900">
  <!-- Header / Nav -->
  <header class="w-full bg-white/60 backdrop-blur sticky top-0 z-40 shadow-sm">
    <div class="max-w-[var(--container)] mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <a href="index.html" class="flex items-center gap-3">
          <div class="w-9 h-9 rounded-lg bg-primary flex items-center justify-center text-white font-bold">A</div>
          <div class="hidden sm:block">
            <div class="text-lg font-semibold">Academia</div>
            <div class="text-xs text-slate-500 -mt-1">Learn. Certify. Grow.</div>
          </div>
        </a>
        <nav class="hidden md:flex items-center gap-6 text-sm">
          <a href="#courses" class="hover:text-primary">Courses</a>
          <a href="#instructors" class="hover:text-primary">Instructors</a>
          <a href="#pricing" class="hover:text-primary">Pricing</a>
          <a href="about.html" class="hover:text-primary">About</a>
          <a href="#faq" class="hover:text-primary">FAQ</a>
        </nav>
        <div class="flex items-center gap-3">
          <a href="login.html" class="text-sm hidden md:inline">Log in</a>
          <a href="signup.html" class="inline-flex items-center px-4 py-2 rounded-md bg-primary text-white text-sm shadow hover:brightness-95">Get started</a>
          <button id="mobileBtn" aria-label="Open menu" class="md:hidden p-2 rounded-md focus-ring">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/></svg>
          </button>
        </div>
      </div>
    </div>
    <!-- Mobile Menu -->
    <div id="mobileMenu" class="md:hidden hidden border-t">
      <div class="px-4 pt-4 pb-6">
        <a href="#courses" class="block py-2">Courses</a>
        <a href="#instructors" class="block py-2">Instructors</a>
        <a href="#pricing" class="block py-2">Pricing</a>
        <a href="about.html" class="block py-2">About</a>
        <div class="mt-4 flex gap-2">
          <a href="login.html" class="flex-1 text-center py-2">Log in</a>
          <a href="signup.html" class="flex-1 text-center py-2 bg-primary text-white rounded">Sign up</a>
        </div>
      </div>
    </div>
  </header>

  <main class="mt-8">
    <!-- HERO -->
    <section class="max-w-[var(--container)] mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
        <div class="lg:col-span-7">
          <div class="text-sm inline-flex items-center px-3 py-1 rounded-full bg-green-50 text-green-800 mb-4">Trusted by universities & companies</div>
          <h1 class="text-4xl sm:text-5xl font-extrabold leading-tight">Turn talent into careers — learn skills employers hire for.</h1>
          <p class="mt-4 text-slate-600 max-w-xl">Expert-led courses, live sessions, verified certificates, and hands-on projects designed to get you job-ready faster. Join thousands of learners and companies leveling up with Academia.</p>
          <div class="mt-6 flex gap-3">
            <a href="signup.html" class="inline-flex items-center px-5 py-3 rounded-lg bg-primary text-white font-semibold shadow hover:brightness-95">Start free trial</a>
            <a href="#courses" class="inline-flex items-center px-4 py-3 rounded-lg border">Explore courses</a>
          </div>
          <div class="mt-6 text-sm text-slate-500">Over <strong>25,000</strong> students trained • <strong>4.8</strong> average rating • Certificates issued</div>
        </div>

        <div class="lg:col-span-5">
          <div class="relative">
            <div class="w-full rounded-2xl overflow-hidden shadow-2xl">
              <img src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?q=80&w=1200&auto=format&fit=crop&ixlib=rb-4.0.3&s=3d44f1b8d3f2e4b9d3fe1b2a3b2c0a8f" alt="dashboard mockup" class="w-full h-64 object-cover" loading="lazy"/>
            </div>
            <div class="absolute -bottom-6 left-6 w-72 p-4 bg-white rounded-xl shadow-lg glass">
              <div class="text-xs text-slate-500">Featured course</div>
              <div class="font-semibold">Full-Stack Web Development</div>
              <div class="text-xs text-slate-500 mt-2">Learn React-style component architecture with practical projects.</div>
              <div class="mt-3 flex gap-2 text-xs">
                <button class="px-3 py-2 rounded bg-primary text-white text-[13px]">Enroll</button>
                <button class="px-3 py-2 rounded border">Preview</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- TRUST / logos -->
    <section class="mt-12 border-t py-8">
      <div class="max-w-[var(--container)] mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between gap-4 overflow-auto no-scrollbar">
          <img src="https://dummyimage.com/140x40/eee/aaa&text=Uni+1" alt="partner" class="h-10"/>
          <img src="https://dummyimage.com/140x40/eee/aaa&text=Uni+2" alt="partner" class="h-10"/>
          <img src="https://dummyimage.com/140x40/eee/aaa&text=Corp+X" alt="partner" class="h-10"/>
          <img src="https://dummyimage.com/140x40/eee/aaa&text=Edu+Lab" alt="partner" class="h-10"/>
          <img src="https://dummyimage.com/140x40/eee/aaa&text=Org+Y" alt="partner" class="h-10"/>
        </div>
      </div>
    </section>

    <!-- FEATURES -->
    <section class="mt-12">
      <div class="max-w-[var(--container)] mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="p-6 bg-white rounded-xl shadow"><div class="inline-flex items-center justify-center w-12 h-12 rounded-md bg-green-50 text-green-700">📚</div><h3 class="mt-4 font-semibold">Pathways & Certificates</h3><p class="mt-2 text-sm text-slate-500">Structured learning paths with verified certificates and projects.</p></div>
          <div class="p-6 bg-white rounded-xl shadow"><div class="inline-flex items-center justify-center w-12 h-12 rounded-md bg-indigo-50 text-indigo-700">🎥</div><h3 class="mt-4 font-semibold">Live Classes & Recordings</h3><p class="mt-2 text-sm text-slate-500">Attend live sessions or learn from recorded lessons on your schedule.</p></div>
          <div class="p-6 bg-white rounded-xl shadow"><div class="inline-flex items-center justify-center w-12 h-12 rounded-md bg-yellow-50 text-yellow-700">🧭</div><h3 class="mt-4 font-semibold">Assessments & Badges</h3><p class="mt-2 text-sm text-slate-500">Skill checks and badges to showcase mastery to employers.</p></div>
        </div>
      </div>
    </section>

    <!-- COURSES CAROUSEL -->
    <section id="courses" class="mt-12">
      <div class="max-w-[var(--container)] mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between">
          <h2 class="text-2xl font-semibold">Featured courses</h2>
          <a href="courses.html" class="text-sm">View all courses →</a>
        </div>
        <div class="mt-6 relative">
          <div id="courseTrack" class="flex gap-4 overflow-x-auto pb-4 no-scrollbar">
            <!-- Course cards (repeatable) -->
            <article class="min-w-[280px] bg-white rounded-xl p-4 shadow">
              <img src="https://images.unsplash.com/photo-1521790366069-9a5a925e90b8?q=80&w=800&auto=format&fit=crop&ixlib=rb-4.0.3&s=6f8b1e6c" alt="course" class="w-full h-36 object-cover rounded-md" loading="lazy"/>
              <h3 class="mt-3 font-semibold">Full-Stack Web Development</h3>
              <p class="text-xs text-slate-500 mt-2">Build production-ready apps with HTML, Tailwind, JS & server APIs.</p>
              <div class="mt-3 flex items-center justify-between text-sm">
                <div>By <strong>Jane Doe</strong></div>
                <div class="text-yellow-500">★ 4.9</div>
              </div>
              <div class="mt-3 flex gap-2">
                <button class="px-3 py-2 rounded bg-primary text-white text-sm">Enroll</button>
                <button class="px-3 py-2 rounded border text-sm" onclick="openCourseModal('fullstack')">Preview</button>
              </div>
            </article>

            <article class="min-w-[280px] bg-white rounded-xl p-4 shadow">
              <img src="https://images.unsplash.com/photo-1517694712202-14dd9538aa97?q=80&w=800&auto=format&fit=crop&ixlib=rb-4.0.3&s=12d6f67f" alt="course" class="w-full h-36 object-cover rounded-md" loading="lazy"/>
              <h3 class="mt-3 font-semibold">Data Science & Machine Learning</h3>
              <p class="text-xs text-slate-500 mt-2">From data cleaning to model deployment workflows.</p>
              <div class="mt-3 flex items-center justify-between text-sm">
                <div>By <strong>Dr. Musa</strong></div>
                <div class="text-yellow-500">★ 4.8</div>
              </div>
              <div class="mt-3 flex gap-2">
                <button class="px-3 py-2 rounded bg-primary text-white text-sm">Enroll</button>
                <button class="px-3 py-2 rounded border text-sm" onclick="openCourseModal('datasci')">Preview</button>
              </div>
            </article>

            <article class="min-w-[280px] bg-white rounded-xl p-4 shadow">
              <img src="https://images.unsplash.com/photo-1504384308090-c894fdcc538d?q=80&w=800&auto=format&fit=crop&ixlib=rb-4.0.3&s=9f7bdd" alt="course" class="w-full h-36 object-cover rounded-md" loading="lazy"/>
              <h3 class="mt-3 font-semibold">Product Design & UX</h3>
              <p class="text-xs text-slate-500 mt-2">Practical UX workflows and portfolio-ready projects.</p>
              <div class="mt-3 flex items-center justify-between text-sm">
                <div>By <strong>Amaka</strong></div>
                <div class="text-yellow-500">★ 4.7</div>
              </div>
              <div class="mt-3 flex gap-2">
                <button class="px-3 py-2 rounded bg-primary text-white text-sm">Enroll</button>
                <button class="px-3 py-2 rounded border text-sm" onclick="openCourseModal('ux')">Preview</button>
              </div>
            </article>

          </div>
        </div>
      </div>
    </section>

    <!-- INSTRUCTORS -->
    <section id="instructors" class="mt-12">
      <div class="max-w-[var(--container)] mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between">
          <h2 class="text-2xl font-semibold">Top instructors</h2>
          <a href="instructors.html" class="text-sm">See all instructors →</a>
        </div>
        <div class="mt-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
          <!-- Instructor card -->
          <div class="bg-white rounded-xl p-4 shadow">
            <div class="flex items-center gap-4">
              <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?q=80&w=400&auto=format&fit=crop&ixlib=rb-4.0.3&s=6a2a" alt="instructor" class="w-14 h-14 rounded-full object-cover" loading="lazy"/>
              <div>
                <div class="font-semibold">Jane Doe</div>
                <div class="text-xs text-slate-500">Senior Web Instructor • 10k students</div>
              </div>
            </div>
            <p class="mt-3 text-sm text-slate-500">Jane teaches practical full-stack workflows — projects, reviews, and live office hours.</p>
            <div class="mt-3 flex gap-2">
              <button class="px-3 py-2 rounded border text-sm" onclick="openInstructorModal('jane')">View profile</button>
              <button class="px-3 py-2 rounded bg-primary text-white text-sm" onclick="openBooking('jane')">Book trial</button>
            </div>
          </div>

          <div class="bg-white rounded-xl p-4 shadow">
            <div class="flex items-center gap-4">
              <img src="https://images.unsplash.com/photo-1545996124-1b3d9c5c42c7?q=80&w=400&auto=format&fit=crop&ixlib=rb-4.0.3&s=8b5f" alt="instructor" class="w-14 h-14 rounded-full object-cover" loading="lazy"/>
              <div>
                <div class="font-semibold">Dr. Musa</div>
                <div class="text-xs text-slate-500">Data Scientist • 7k students</div>
              </div>
            </div>
            <p class="mt-3 text-sm text-slate-500">Hands-on data science with deployment-focused projects.</p>
            <div class="mt-3 flex gap-2">
              <button class="px-3 py-2 rounded border text-sm" onclick="openInstructorModal('musa')">View profile</button>
              <button class="px-3 py-2 rounded bg-primary text-white text-sm" onclick="openBooking('musa')">Book trial</button>
            </div>
          </div>

          <div class="bg-white rounded-xl p-4 shadow">
            <div class="flex items-center gap-4">
              <img src="https://images.unsplash.com/photo-1544723795-3fb6469f5b39?q=80&w=400&auto=format&fit=crop&ixlib=rb-4.0.3&s=4b0" alt="instructor" class="w-14 h-14 rounded-full object-cover" loading="lazy"/>
              <div>
                <div class="font-semibold">Amaka</div>
                <div class="text-xs text-slate-500">Lead UX Designer • 4k students</div>
              </div>
            </div>
            <p class="mt-3 text-sm text-slate-500">Design systems, prototyping, and portfolio coaching.</p>
            <div class="mt-3 flex gap-2">
              <button class="px-3 py-2 rounded border text-sm" onclick="openInstructorModal('amaka')">View profile</button>
              <button class="px-3 py-2 rounded bg-primary text-white text-sm" onclick="openBooking('amaka')">Book trial</button>
            </div>
          </div>

        </div>
      </div>
    </section>

    <!-- HOW IT WORKS -->
    <section class="mt-12 bg-gradient-to-b from-white to-gray-50 py-12">
      <div class="max-w-[var(--container)] mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-semibold">How it works</h2>
        <div class="mt-6 grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="p-6 bg-white rounded-xl shadow text-center">
            <div class="text-3xl">1</div>
            <h4 class="mt-3 font-semibold">Choose a path</h4>
            <p class="text-sm text-slate-500 mt-2">Select role-based learning pathways.</p>
          </div>
          <div class="p-6 bg-white rounded-xl shadow text-center">
            <div class="text-3xl">2</div>
            <h4 class="mt-3 font-semibold">Learn at your pace</h4>
            <p class="text-sm text-slate-500 mt-2">Mix live classes and projects.</p>
          </div>
          <div class="p-6 bg-white rounded-xl shadow text-center">
            <div class="text-3xl">3</div>
            <h4 class="mt-3 font-semibold">Assess & Certify</h4>
            <p class="text-sm text-slate-500 mt-2">Quizzes and mentor reviews for certificates.</p>
          </div>
          <div class="p-6 bg-white rounded-xl shadow text-center">
            <div class="text-3xl">4</div>
            <h4 class="mt-3 font-semibold">Get job-ready</h4>
            <p class="text-sm text-slate-500 mt-2">Hiring partners and portfolio reviews.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- PRICING & FAQ -->
    <section id="pricing" class="mt-12 py-12">
      <div class="max-w-[var(--container)] mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between">
          <h2 class="text-2xl font-semibold">Plans that scale with you</h2>
          <div class="flex items-center gap-2 text-sm">
            <span class="text-slate-500">Monthly</span>
            <label class="relative inline-flex items-center cursor-pointer">
              <input id="priceToggle" type="checkbox" class="sr-only">
              <div class="w-11 h-6 bg-gray-200 rounded-full peer"></div>
              <div class="absolute left-0.5 top-0.5 w-5 h-5 bg-white rounded-full transition peer-checked:translate-x-5 peer-checked:bg-primary shadow"></div>
            </label>
            <span class="text-slate-500">Yearly</span>
          </div>
        </div>

        <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="p-6 bg-white rounded-2xl shadow">
            <div class="text-sm text-slate-500">Free</div>
            <div class="mt-2 text-3xl font-extrabold"><span class="num-anim" data-month="0" data-year="0">0</span><span class="text-sm">/mo</span></div>
            <p class="mt-3 text-sm text-slate-500">Access to selected free courses and community.</p>
            <ul class="mt-4 text-sm space-y-2 text-slate-600">
              <li>Community access</li>
              <li>Limited projects</li>
            </ul>
            <div class="mt-6">
              <button class="w-full py-2 rounded bg-primary text-white">Get started</button>
            </div>
          </div>

          <div class="p-6 bg-white rounded-2xl shadow ring-2 ring-primary/10">
            <div class="text-sm text-slate-500">Pro</div>
            <div class="mt-2 text-3xl font-extrabold"><span class="num-anim" data-month="29" data-year="290">29</span><span class="text-sm">/mo</span></div>
            <p class="mt-3 text-sm text-slate-500">All courses, certificates, career support.</p>
            <ul class="mt-4 text-sm space-y-2 text-slate-600">
              <li>Unlimited courses</li>
              <li>1:1 mentorship sessions</li>
              <li>Project reviews</li>
            </ul>
            <div class="mt-6">
              <button class="w-full py-2 rounded bg-primary text-white">Start free trial</button>
            </div>
          </div>

          <div class="p-6 bg-white rounded-2xl shadow">
            <div class="text-sm text-slate-500">Enterprise</div>
            <div class="mt-2 text-3xl font-extrabold"><span class="num-anim" data-month="199" data-year="1990">199</span><span class="text-sm">/mo</span></div>
            <p class="mt-3 text-sm text-slate-500">Custom training, analytics, and SSO for teams.</p>
            <ul class="mt-4 text-sm space-y-2 text-slate-600">
              <li>Dedicated success manager</li>
              <li>Bulk licensing</li>
            </ul>
            <div class="mt-6">
              <button class="w-full py-2 rounded border">Contact sales</button>
            </div>
          </div>
        </div>

        <div id="faq" class="mt-8">
          <h3 class="font-semibold">Common questions</h3>
          <div class="mt-3 space-y-2">
            <details class="p-4 bg-white rounded"><summary class="cursor-pointer">Can I cancel my subscription?</summary><p class="mt-2 text-sm text-slate-500">Yes — you can cancel any time from your billing settings.</p></details>
            <details class="p-4 bg-white rounded"><summary class="cursor-pointer">Do you offer certificates?</summary><p class="mt-2 text-sm text-slate-500">Yes — verified certificates for completed paths.</p></details>
          </div>
        </div>
      </div>
    </section>

    <!-- TESTIMONIALS & FINAL CTA -->
    <section class="mt-12 py-12 bg-white">
      <div class="max-w-[var(--container)] mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-2xl font-semibold">What students say</h2>
        <div class="mt-6 max-w-3xl mx-auto">
          <blockquote class="text-lg italic">"The project-based courses and mentorship helped me land my first job as a frontend developer."</blockquote>
          <div class="mt-4">— <strong>Olu</strong>, Frontend Developer</div>
        </div>

        <div class="mt-8 bg-primary/5 p-6 rounded-lg inline-flex items-center gap-4">
          <div class="text-lg font-semibold">Start your 7-day free trial</div>
          <form class="ml-4 flex sm:ml-8">
            <input type="email" required placeholder="Your email" class="px-3 py-2 rounded-l-md border focus-ring" />
            <button class="px-4 py-2 bg-primary text-white rounded-r-md">Get started</button>
          </form>
        </div>
      </div>
    </section>

  </main>

  <!-- FOOTER -->
  <footer class="mt-12 bg-slate-900 text-white">
    <div class="max-w-[var(--container)] mx-auto px-4 sm:px-6 lg:px-8 py-12 grid grid-cols-1 md:grid-cols-3 gap-6">
      <div>
        <div class="font-semibold text-xl">Academia</div>
        <div class="text-sm text-slate-300 mt-2">Learn. Certify. Grow.</div>
      </div>
      <div>
        <div class="font-semibold">Company</div>
        <ul class="mt-3 text-sm text-slate-300 space-y-2">
          <li><a href="about.html">About</a></li>
          <li><a href="#">Careers</a></li>
          <li><a href="#">Contact</a></li>
        </ul>
      </div>
      <div>
        <div class="font-semibold">Resources</div>
        <ul class="mt-3 text-sm text-slate-300 space-y-2">
          <li><a href="courses.html">Courses</a></li>
          <li><a href="#">Blog</a></li>
          <li><a href="#">Help</a></li>
        </ul>
      </div>
    </div>
    <div class="border-t border-slate-800 py-4 text-center text-sm text-slate-400">© 2025 Academia. All rights reserved.</div>
  </footer>

  <!-- Modals (hidden) -->
  <div id="modal" class="fixed inset-0 hidden items-center justify-center z-50 bg-black/40">
    <div class="bg-white rounded-lg max-w-2xl w-full p-6 relative">
      <button onclick="closeModal()" class="absolute right-4 top-4 text-slate-500">✕</button>
      <div id="modalContent"></div>
    </div>
  </div>

  <script>
    // Basic interactive behaviors
    const mobileBtn = document.getElementById('mobileBtn');
    const mobileMenu = document.getElementById('mobileMenu');
    mobileBtn.addEventListener('click', ()=> mobileMenu.classList.toggle('hidden'));

    // Pricing toggle
    const priceToggle = document.getElementById('priceToggle');
    function updatePrices(yearly){
      document.querySelectorAll('.num-anim').forEach(el=>{
        const month = el.getAttribute('data-month');
        const year = el.getAttribute('data-year');
        el.textContent = yearly ? year : month;
      })
    }
    priceToggle.addEventListener('change', (e)=> updatePrices(e.target.checked));

    // Modal system
    function openModal(html){
      const modal = document.getElementById('modal');
      const content = document.getElementById('modalContent');
      content.innerHTML = html;
      modal.classList.remove('hidden');
      modal.classList.add('flex');
      document.body.style.overflow = 'hidden';
    }
    function closeModal(){
      const modal = document.getElementById('modal');
      modal.classList.add('hidden');
      modal.classList.remove('flex');
      document.body.style.overflow = '';
    }

    // Course / instructor modals
    function openCourseModal(id){
      let html = '<h3 class="text-xl font-semibold">Course preview</h3>';
      html += '<p class="mt-3 text-sm text-slate-600">Course id: '+id+'</p>';
      html += '<div class="mt-4 flex gap-2"><button class="px-3 py-2 rounded bg-primary text-white">Enroll</button><button class="px-3 py-2 rounded border" onclick="closeModal()">Close</button></div>';
      openModal(html);
    }
    function openInstructorModal(id){
      let html = '<h3 class="text-xl font-semibold">Instructor profile</h3>';
      html += '<p class="mt-3 text-sm text-slate-600">Profile: '+id+'</p>';
      html += '<div class="mt-4 flex gap-2"><button class="px-3 py-2 rounded bg-primary text-white">Follow</button><button class="px-3 py-2 rounded border" onclick="closeModal()">Close</button></div>';
      openModal(html);
    }
    function openBooking(id){
      let html = '<h3 class="text-xl font-semibold">Book a trial with '+id+'</h3>';
      html += '<form class="mt-3 space-y-2">';
      html += '<input type="email" placeholder="Your email" class="w-full p-2 border rounded focus-ring" required />';
      html += '<input type="datetime-local" class="w-full p-2 border rounded focus-ring" required />';
      html += '<div class="flex gap-2"><button class="px-3 py-2 rounded bg-primary text-white">Request</button><button type="button" class="px-3 py-2 rounded border" onclick="closeModal()">Cancel</button></div>';
      html += '</form>';
      openModal(html);
    }

    // Close modal on backdrop click
    document.getElementById('modal').addEventListener('click', (e)=>{ if(e.target.id==='modal') closeModal(); });

    // Simple scroll spy for header active link (mobile omitted)
    const sections = ['courses','instructors','pricing','faq'];
    function onScrollSpy(){
      const y = window.scrollY + 120;
      sections.forEach(id=>{
        const el = document.getElementById(id);
        if(!el) return;
        const link = document.querySelector('a[href="#'+id+'"]').classList;
        if(y >= el.offsetTop && y < el.offsetTop + el.offsetHeight) link.add('text-primary'); else link.remove('text-primary');
      })
    }
    window.addEventListener('scroll', onScrollSpy);

    // Init
    updatePrices(false);
  </script>
</body>
</html>
