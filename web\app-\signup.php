<!doctype html>
<html lang="en" class="h-100">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="description" content="">
  <meta name="author" content="">
  <meta name="generator" content="">
  <title><?= $config['site_name'] ?> - Register</title>



  <!-- Favicons -->
  <link rel="apple-touch-icon" href="assets/img/dinaplug-logo.png" sizes="180x180">
  <link rel="apple-touch-icon" href="assets/img/dinaplug-logo.png" sizes="152x152">
  <link rel="apple-touch-icon" href="assets/img/dinaplug-logo.png" sizes="167x167">
  <link rel="apple-touch-icon" href="assets/img/dinaplug-logo.png" sizes="120x120">
  <link rel="icon" href="assets/img/favicon32.png" sizes="32x32" type="image/png">
  <link rel="icon" href="assets/img/favicon16.png" sizes="16x16" type="image/png">

  <!-- SweetAlert2 -->
  <link href="//cdn.jsdelivr.net/npm/@sweetalert2/theme-default/default.css" rel="stylesheet">
  <script src="//cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"></script>

  <!-- Google fonts-->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">

  <!-- bootstrap icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css">

  <!-- style css for this template -->
  <link href="assets/css/style.css" rel="stylesheet" id="style">

  <style>
    body {
      padding-bottom: 75px !important;
      background-color: #f8f9fa;
    }
    .header {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 998;
      background-color: #ffffff;
      padding: 10px 15px;
      border-bottom: 1px solid #dee2e6;
    }
    .main-container {
      padding-top: 70px;
      padding-bottom: 20px;
    }
    .btn-primary, .btn-default, .nav-link.active {
      background-color: #3498db !important;
      border-color: #2980b9 !important;
    }
    .btn-primary:hover, .btn-default:hover {
      background-color: #2980b9 !important;
    }
    .text-primary {
      color: #3498db !important;
    }
    .border-primary {
      border-color: #3498db !important;
    }
    .form-floating.is-invalid .form-control {
      border-color: #3498db;
    }
    .tooltip-btn {
      color: #3498db !important;
    }
    .footer {
      border-top: 1px solid #dee2e6;
    }
    .footer .nav-link {
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      color: #6c757d;
      text-align: center;
      transition: all 0.2s ease-in-out;
      border-radius: 8px;
      margin: 0 2px;
    }
    .footer .nav-link.active {
      color: #ffffff !important;
      background-color: #3498db;
    }
    .footer .nav-link:not(.active):hover {
      color: #3498db;
      background-color: #e9ecef;
    }
    .footer .nav-link span {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .footer .nav-icon {
      margin-bottom: 0.15rem;
      font-size: 1.2rem;
    }
    .footer .nav-text {
      font-size: 0.7rem;
      line-height: 1;
      font-weight: 500;
    }
    .app-logo {
      height: 40px;
    }
    /* Password toggle button styling */
    .password-toggle-btn {
      border: none !important;
      background: none !important;
      color: #6c757d !important;
      z-index: 10;
      padding: 0 !important;
      margin: 0 !important;
      line-height: 1;
    }
    .password-toggle-btn:hover {
      color: #495057 !important;
    }
    .password-toggle-btn:focus {
      box-shadow: none !important;
      outline: none !important;
    }
  </style>
</head>

<?php
include "../../config.php";

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Check DB connection immediately after include
if (!$con) {
  die("Database connection failed immediately after include in signup.php: " . mysqli_connect_error());
}

$refUsername = '';
if (isset($_GET['referrer'])) {
  // Remove deprecated filter
  $refU = strip_tags(mysqli_real_escape_string($con, trim(preg_replace('/[\t\n\r\s]+/', ' ', $_GET['referrer']))));
  $checkSql = mysqli_query($con, "SELECT username FROM users WHERE username = '$refU'");
  if (!$checkSql) { die("Error checking referrer: " . mysqli_error($con)); }
  if (mysqli_num_rows($checkSql) == 1) {
    $userDet = mysqli_fetch_assoc($checkSql);
    $refUsername = $userDet['username'];
  } else {
    echo "<script>alert('INVALID REFERAL ID')</script>";
  }
}
if (isset($_POST['reg'])) {
  // Remove deprecated filters & incorrect escaping
  $name = trim(strip_tags(mysqli_real_escape_string($con, trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['name'])))));
  $email = trim(strip_tags(mysqli_real_escape_string($con, trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['email'])))));
  $username = trim(strip_tags(mysqli_real_escape_string($con, trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['username'])))));
  $phone = trim(strip_tags(mysqli_real_escape_string($con, trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['phone'])))));
  $refer_input = isset($_POST['refer']) ? $_POST['refer'] : '';
  $refer = trim(strip_tags(trim(preg_replace('/[\t\n\r\s]+/', ' ', $refer_input))));
  $pass = trim(strip_tags(trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['pass']))));
  $repeat_pass = trim(strip_tags(trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['repeat_pass']))));

  if (empty($name)) {
    $_SESSION['reg_msg'] = 'Full Name is Required';
    goto end;
  } elseif (empty($email)) {
    $_SESSION['reg_msg'] = 'Email is Required';
    goto end;
  } elseif (empty($username)) {
    $_SESSION['reg_msg'] = 'username is Required';
    goto end;
  } elseif (str_word_count($name) < 2) {
    $_SESSION['reg_msg'] = 'Name must include last-name and first-name';
    goto end;
  } elseif (empty($phone)) {
    $_SESSION['reg_msg'] = 'Phone Number is Required';
    goto end;
  } elseif (strlen($phone) != 11) {
    $_SESSION['reg_msg'] = 'Invalid Phone Number';
    goto end;
  } elseif (empty($pass)) {
    $_SESSION['reg_msg'] = 'Password is Required';
    goto end;
  } elseif (empty($repeat_pass)) {
    $_SESSION['reg_msg'] = 'Repeat Password is Required';
    goto end;
  } elseif ($pass != $repeat_pass) {
    $_SESSION['reg_msg'] = "Passwords aren't Matched";
    goto end;
  } elseif (strlen($pass) < 8) {
    $_SESSION['reg_msg'] = "Passwords length must be at least 8";
    goto end;
  } else {
    if (mysqli_num_rows(mysqli_query($con, "SELECT id FROM users WHERE email='$email'")) != 0) {
      $_SESSION['reg_msg'] = "Email has already been used";
      goto end;
    } elseif (mysqli_num_rows(mysqli_query($con, "SELECT id FROM users WHERE username='$username'")) != 0) {
      $_SESSION['reg_msg'] = "Username has already been used";
      goto end;
    } elseif (mysqli_num_rows(mysqli_query($con, "SELECT id FROM users WHERE phone='$phone'")) != 0) {
      $_SESSION['reg_msg'] = "Phone Number has already been used";
      goto end;
    } elseif (!empty($refer) && mysqli_num_rows(mysqli_query($con, "SELECT id FROM users WHERE username='$refer'")) == 0) {
      $_SESSION['reg_msg'] = "INVALID REFERRER ID";
      goto end;
    }
    $password = md5($pass);

    // Add check for the SELECT query before referral update
    $selectReferQuery = "SELECT refs FROM users WHERE username = '$refer'";
    $referResult = mysqli_query($con, $selectReferQuery);
    if (!$referResult) { die("Error fetching referrer details: " . mysqli_error($con)); }
    $oldUserRefs = mysqli_fetch_assoc($referResult);

    if ($oldUserRefs) { // Proceed only if referrer was found
      $refs = json_decode($oldUserRefs['refs']);
      if (json_last_error() !== JSON_ERROR_NONE || !is_array($refs)) {
        $refs = [];
      }
      array_push($refs, $username);
      $newRefs = json_encode($refs);
      // Add check for the UPDATE query
      $updateReferQuery = "UPDATE users SET refs = '$newRefs' WHERE username = '$refer'";
      if (!mysqli_query($con, $updateReferQuery)) {
        die("Error updating referrer refs: " . mysqli_error($con));
      }
    }

    $token = md5($current_date . $username . 'VTU');
    // Add check for the INSERT query - include necessary columns with defaults
    $insertQuery = "INSERT INTO users (name, email, username, phone, refBy, regDate, password, token, refs, compRef, status, address, staff, otp, day_spent, bank_added, bank, max_airtime) VALUES ('$name', '$email', '$username', '$phone', '$refer', '$current_date', '$password', '$token', '[]', '[]', 'verified', '', '0', '0', '0', '0', '', '0')";
    if (mysqli_query($con, $insertQuery)) {
      $_SESSION['reg_s_msg'] = "Registered Successfully";
    } else {
      $_SESSION['reg_msg'] = "Database error during registration: " . mysqli_error($con);
    }
  }
}
end:

if (isset($_SESSION['reg_msg']) && !empty($_SESSION['reg_msg'])) {
?>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      Swal.fire("<?= $_SESSION["reg_msg"] ?>", '', 'error').then(() => {
        window.location.replace("signup.php");
      });
    });
  </script>
<?php
  $_SESSION['reg_msg'] = '';
}
if (isset($_SESSION['reg_s_msg']) && !empty($_SESSION['reg_s_msg'])) {
?>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      Swal.fire("<?= $_SESSION["reg_s_msg"] ?>", 'Press OK button and I will take you to the login page!', 'success').then(() => {
        window.location.replace("login.php");
      });
    });
  </script>
<?php
  $_SESSION['reg_s_msg'] = '';
}
?>

<body class="body-scroll d-flex flex-column">

  <!-- loader section -->
  <div class="container-fluid loader-wrap" style="display:none;">
    <div class="row h-100">
      <div class="col-10 col-md-6 col-lg-5 col-xl-3 mx-auto text-center align-self-center">
        <div class="logo-wallet">
          <div class="wallet-bottom">
          </div>
          <div class="wallet-cards"></div>
          <div class="wallet-top">
          </div>
        </div>
        <p class="mt-4"><span class="text-secondary">Loading To <?= $config['site_name'] ?></span><br><strong>Please
            Wait...</strong></p>
      </div>
    </div>
  </div>
  <!-- loader section ends -->

  <!-- Header -->
  <!-- Header removed -->
  <!-- Header ends -->

  <!-- Begin page content -->
  <main class="container-fluid h-100">
    <div class="main-container container">
      <div class="row justify-content-center">
        <div class="col-12 col-md-8 col-lg-6">
          <!-- Sign Up Card -->
          <div class="card shadow-sm border-0 rounded-3 mt-4">
            <div class="card-body p-4">
              <h2 class="card-title text-center mb-4">Create New Account</h2>

              <form role="form" method="POST">
                <!-- Referrer (Optional Field) -->
                <?php if ($refUsername != ''): // Only show if a valid referrer exists ?>
                <div class="mb-3">
                  <label for="refer" class="form-label">Referrer</label>
                  <input class="form-control bg-light" id="refer" autocomplete="off" type="text" name="refer" value="<?= htmlspecialchars($refUsername) ?>" readonly placeholder="Referrer Username">
                </div>
                <?php endif; ?>

                <!-- Full Name -->
                <div class="mb-3">
                  <label for="name" class="form-label">Full Name</label>
                  <input class="form-control" id="name" type="text" name="name" placeholder="First and Last Name" required>
                </div>

                <!-- Email -->
                <div class="mb-3">
                  <label for="email" class="form-label">Email</label>
                  <input class="form-control" id="email" type="email" name="email" placeholder="Email Address" required>
                </div>

                <!-- Username -->
                <div class="mb-3">
                  <label for="username" class="form-label">Username</label>
                  <input class="form-control" id="username" type="text" name="username" placeholder="Choose a username" required>
                </div>

                <!-- Phone -->
                <div class="mb-3">
                  <label for="phone" class="form-label">Phone Number</label>
                  <input class="form-control" id="phone" type="tel" name="phone" placeholder="Phone Number" required>
                </div>

                <!-- Password -->
                <div class="mb-3 position-relative">
                  <label for="pass" class="form-label">Password</label>
                  <div class="position-relative">
                    <input class="form-control pe-5" id="pass" type="password" name="pass" placeholder="Password (min 8 characters)" required>
                    <button type="button" class="btn password-toggle-btn position-absolute top-50 end-0 translate-middle-y me-2" onclick="togglePasswordVisibility('pass', this)">
                      <i class="bi bi-eye-slash"></i>
                    </button>
                  </div>
                </div>

                <!-- Repeat Password -->
                <div class="mb-3 position-relative">
                  <label for="repeat_pass" class="form-label">Confirm Password</label>
                  <div class="position-relative">
                    <input class="form-control pe-5" id="repeat_pass" type="password" name="repeat_pass" placeholder="Repeat Password" required>
                    <button type="button" class="btn password-toggle-btn position-absolute top-50 end-0 translate-middle-y me-2" onclick="togglePasswordVisibility('repeat_pass', this)">
                      <i class="bi bi-eye-slash"></i>
                    </button>
                  </div>
                </div>

                <?php if ($refUsername == ''): // Only show if no referrer specified ?>
                <!-- Manual Referrer Field (optional) -->
                <div class="mb-3">
                  <label for="refer" class="form-label">Referrer Username (Optional)</label>
                  <input class="form-control" id="refer" type="text" name="refer" placeholder="Referral ID (optional)">
                </div>
                <?php endif; ?>

                <div class="d-grid gap-2 mt-4">
                  <button type="submit" name="reg" class="btn btn-primary">
                    <i class="bi bi-person-plus"></i> Create Account
                  </button>
                </div>

                <div class="mt-4 text-center">
                  <p class="mb-0">Already have an account? <a href="login.php" class="text-decoration-none">Sign In</a></p>
                </div>
              </form>
              <script>
                function togglePasswordVisibility(inputId, iconElem) {
                  var input = document.getElementById(inputId);
                  var icon = iconElem.querySelector('i');
                  if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('bi-eye-slash');
                    icon.classList.add('bi-eye');
                  } else {
                    input.type = 'password';
                    icon.classList.remove('bi-eye');
                    icon.classList.add('bi-eye-slash');
                  }
                }
              </script>
            </div>
          </div>
        </div>
      </div>
    </div> <!-- main-container -->
  </main>

  <!-- Footer -->
  <!-- Footer removed -->
  <!-- Footer ends-->

  <!-- Required jquery and libraries -->
  <script src="assets/js/jquery-3.3.1.min.js"></script>
  <script src="assets/js/popper.min.js"></script>
  <script src="assets/vendor/bootstrap-5/js/bootstrap.bundle.min.js"></script>
  
  <!-- Customized jquery file  -->
  <script src="assets/js/main.js"></script>
  <script src="assets/js/color-scheme.js"></script>



  <!-- page level custom script -->
  <script src="assets/js/app.js"></script>

</body>

</html>