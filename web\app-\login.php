<?php
include "../../config.php";

if (isset($_GET['last'])) {
  $_SESSION['lastPage'] = $_GET['last'];
}
if (isset($_POST['login'])) {

  $username = strip_tags(mysqli_real_escape_string($con, trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['username']))));
  $pass = strip_tags(mysqli_real_escape_string($con, trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['pass']))));
  if (empty($username)) {
    $_SESSION['log_err'] = "Username is Required";
  } elseif (empty($pass)) {
    $_SESSION['log_err'] = "Password is Required";
  } else {

    $quer = mysqli_query($con, "SELECT * FROM users WHERE email = '$username' OR phone = '$username' OR username = '$username' ");
    if (mysqli_num_rows($quer) == 1) {
      $data = mysqli_fetch_assoc($quer);
      if (md5($pass) == $data['password']) {
        if ($data['suspended'] == 'true') {
          $_SESSION['log_err'] = 'Account Suspended';
          goto endLogin;
        }
        if (empty($data['token']) || $data['token'] == null) {
          $new_token = hash('sha256', time());
          mysqli_query($con, "UPDATE users SET token = '$new_token' WHERE username = '$username'");
        }
        $_SESSION['data'] = $data;
        $_SESSION['username'] = $username;
        $_SESSION['token'] = "1e8789816530b40d8784c371d829db38";
        $_SESSION['LAST_ACTIVITY'] = time();
        $lastPage = '';
        if (isset($_SESSION['lastPage'])) {
          $lastPage = $_SESSION['lastPage'];
?>
          <script type="text/javascript">
            window.location.replace('welcome.php')
          </script>
<?php
        } else {
          header("location:welcome.php");
        }
      } else {
        $_SESSION['log_err'] = "Wrong Password";
      }
    } else {
      $_SESSION['log_err'] = "Account Not Found";
    }
  }
}
endLogin:
?>
<!doctype html>
<html lang="en" class="h-100">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="description" content="">
  <meta name="author" content="">
  <meta name="generator" content="">
  <title><?= $config['site_name'] ?> - Login Page.</title>



  <!-- Favicons -->
  <link rel="apple-touch-icon" href="../sharesublogo.png" sizes="180x180">
  <link rel="apple-touch-icon" href="../sharesublogo.png" sizes="152x152">
  <link rel="apple-touch-icon" href="../sharesublogo.png" sizes="167x167">
  <link rel="apple-touch-icon" href="../sharesublogo.png" sizes="120x120">
  <link rel="icon" href="assets/img/favicon32.png" sizes="32x32" type="image/png">
  <link rel="icon" href="assets/img/favicon16.png" sizes="16x16" type="image/png">

  <!-- SweetAlert2 -->
  <link href="//cdn.jsdelivr.net/npm/@sweetalert2/theme-default/default.css" rel="stylesheet">
  <script src="//cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"></script>

  <!-- Google fonts-->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">

  <!-- bootstrap icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css">

  <!-- style css for this template -->
  <link href="assets/css/style.css" rel="stylesheet" id="style">

  <style>
    body {
      padding-bottom: 75px !important;
      background-color: #f8f9fa;
    }
    .header {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
    }
    /* Password toggle button styling */
    .password-toggle-btn {
      border: none !important;
      background: none !important;
      color: #6c757d !important;
      z-index: 10;
      padding: 0 !important;
      margin: 0 !important;
      line-height: 1;
    }
    .password-toggle-btn:hover {
      color: #495057 !important;
    }
    .password-toggle-btn:focus {
      box-shadow: none !important;
      outline: none !important;
    }
      z-index: 998;
      background-color: #ffffff;
      padding: 10px 15px;
      border-bottom: 1px solid #dee2e6;
    }
    .main-container {
      padding-top: 70px;
      padding-bottom: 20px;
    }
    .btn-primary, .btn-default, .nav-link.active {
      background-color: #3498db !important;
      border-color: #2980b9 !important;
    }
    .btn-primary:hover, .btn-default:hover {
      background-color: #2980b9 !important;
    }
    .text-primary {
      color: #3498db !important;
    }
    .border-primary {
      border-color: #3498db !important;
    }
    .form-floating.is-invalid .form-control {
      border-color: #3498db;
    }
    .tooltip-btn {
      color: #3498db !important;
    }
    .footer {
      border-top: 1px solid #dee2e6;
    }
    .footer .nav-link {
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      color: #6c757d;
      text-align: center;
      transition: all 0.2s ease-in-out;
      border-radius: 8px;
      margin: 0 2px;
    }
    .footer .nav-link.active {
      color: #ffffff !important;
      background-color: #3498db;
    }
    .footer .nav-link:not(.active):hover {
      color: #3498db;
      background-color: #e9ecef;
    }
    .footer .nav-link span {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .footer .nav-icon {
      margin-bottom: 0.15rem;
      font-size: 1.2rem;
    }
    .footer .nav-text {
      font-size: 0.7rem;
      line-height: 1;
      font-weight: 500;
    }
    .app-logo {
      height: 40px;
    }
  </style>
</head>

<body class="body-scroll d-flex flex-column">

  <!-- loader section -->
  <div class="container-fluid loader-wrap" style="display:none;">
    <div class="row h-100">
      <div class="col-10 col-md-6 col-lg-5 col-xl-3 mx-auto text-center align-self-center">
        <div class="logo-wallet">
          <div class="wallet-bottom">
          </div>
          <div class="wallet-cards"></div>
          <div class="wallet-top">
          </div>
        </div>
        <p class="mt-4"><span class="text-secondary">Loading To <?= $config['site_name'] ?></span><br><strong>Please
            Wait...</strong></p>
      </div>
    </div>
  </div>
  <!-- loader section ends -->

  <!-- Begin page content -->
  <main class="container-fluid h-100">
    <div class="main-container container">
      <div class="row justify-content-center">
        <div class="col-12 col-md-8 col-lg-6">
          <!-- Login Card -->
          <div class="card shadow-sm border-0 rounded-3 mt-4">
            <div class="card-body p-4">
              <h2 class="card-title text-center mb-4">Sign in to your account</h2>

              <?php
              if (isset($_SESSION['reg_s_msg']) && !empty($_SESSION['reg_s_msg'])) {
              ?>
                <script>
                  Swal.fire("<?= $_SESSION["reg_s_msg"] ?>", 'Please check your email to verify your email address.', 'success').then(() => {
                    window.location.replace("login.php");
                  })
                </script>
              <?php
                $_SESSION['reg_s_msg'] = '';
              }
              if (isset($_SESSION['log_err']) && !empty($_SESSION['log_err'])) {
              ?>
                <script>
                  Swal.fire("<?= $_SESSION["log_err"] ?>", '', 'error');
                </script>
              <?php
                $_SESSION['log_err'] = '';
              }
              ?>

              <form method="POST" action="login.php" onsubmit="document.getElementById('submit').innerHTML = '<i class=\'bi bi-arrow-clockwise\'></i> Processing...';">
                
                <div class="mb-3">
                  <label for="username" class="form-label">Username or Phone Number</label>
                  <input type="text" id="username" class="form-control" name="username" placeholder="Your username" required="required">
                </div>

                <div class="mb-3 position-relative">
                  <label for="password" class="form-label">Password</label>
                  <div class="position-relative">
                    <input id="password" type="password" class="form-control pe-5" name="pass" placeholder="********" required="required">
                    <button type="button" class="btn password-toggle-btn position-absolute top-50 end-0 translate-middle-y me-2" onclick="togglePasswordVisibility('password', this)">
                      <i class="bi bi-eye-slash" id="togglePasswordIcon"></i>
                    </button>
                  </div>
                </div>

                <div class="mb-3 text-end">
                  <a href="https://<?= $config['site_link'] ?>/web/forgot/" class="text-decoration-none">
                    Forgot your password?
                  </a>
                </div>

                <div class="d-grid gap-2">
                  <button type="submit" name="login" class="btn btn-primary" id="submit">
                    <i class="bi bi-box-arrow-in-right"></i> Sign In
                  </button>
                </div>
                
                <div class="mt-4 text-center">
                  <p class="mb-0">Don't have an account? <a href="signup.php" class="text-decoration-none">Create New Account</a></p>
                </div>
              </form>
              <script>
                function togglePasswordVisibility(inputId, iconElem) {
                  var input = document.getElementById(inputId);
                  var icon = iconElem.querySelector('i');
                  if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('bi-eye-slash');
                    icon.classList.add('bi-eye');
                  } else {
                    input.type = 'password';
                    icon.classList.remove('bi-eye');
                    icon.classList.add('bi-eye-slash');
                  }
                }
              </script>
            </div>
          </div>
        </div>
      </div>
    </div> <!-- main-container -->
  </main>

  <!-- Footer -->
  <!-- Footer removed -->
  <!-- Footer ends-->

  <!-- Required jquery and libraries -->
  <script src="assets/js/jquery-3.3.1.min.js"></script>
  <script src="assets/js/popper.min.js"></script>
  <script src="assets/vendor/bootstrap-5/js/bootstrap.bundle.min.js"></script>
  
  <!-- Customized jquery file  -->
  <script src="assets/js/main.js"></script>
  <script src="assets/js/color-scheme.js"></script>



  <!-- page level custom script -->
  <script src="assets/js/app.js"></script>

</body>

</html>