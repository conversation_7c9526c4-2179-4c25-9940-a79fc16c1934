            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body p-0">
                            <ul class="list-group list-group-flush bg-none">
                
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- main page content ends -->


</main>
    <!-- Page ends-->

    <!-- Footer -->
    <footer class="footer fixed-bottom bg-white shadow-lg">
        <div class="container">
            <?php $currentPage = basename($_SERVER['PHP_SELF']); // Get current page filename ?>
            <nav class="nav nav-pills nav-justified">
                <a class="nav-link <?= ($currentPage == 'welcome.php') ? 'active' : '' ?>" href="welcome.php">
                    <span>
                        <i class="nav-icon bi bi-house-door fs-5"></i>
                        <span class="nav-text d-block small">Home</span>
                    </span>
                </a>
                <a class="nav-link <?= ($currentPage == 'profile.php') ? 'active' : '' ?>" href="profile.php">
                    <span>
                        <i class="nav-icon bi bi-person fs-5"></i>
                        <span class="nav-text d-block small">Profile</span>
                    </span>
                </a>
                <a class="nav-link <?= ($currentPage == 'support.php') ? 'active' : '' ?>" href="support.php">
                     <span>
                        <i class="nav-icon bi bi-telephone fs-5"></i>
                        <span class="nav-text d-block small">Support</span>
                    </span>
                </a>
                <a class="nav-link <?= ($currentPage == 'transactions.php' || $currentPage == 'invoice.php' || $currentPage == 'receipt.php') ? 'active' : '' ?>" href="transactions.php">
                     <!-- Highlight History if on transactions, invoice, or receipt page -->
                    <span>
                        <i class="nav-icon bi bi-clock-history fs-5"></i>
                        <span class="nav-text d-block small">History</span>
                    </span>
                </a>
                 <a class="nav-link <?= ($currentPage == 'more.php') ? 'active' : '' ?>" href="#"> <!-- Link to a 'More' page/modal if needed, e.g., more.php -->
                     <span>
                        <i class="nav-icon bi bi-grid fs-5"></i>
                        <span class="nav-text d-block small">More</span>
                    </span>
                </a>
            </nav>
        </div>
    </footer>
    <!-- Footer ends-->

    <!-- Add this CSS to your stylesheet or keep it here -->
    <style>
        /* Add padding to body to prevent content from being hidden behind fixed footer */
        /* Ensure this rule exists somewhere in your CSS (header or main CSS file) */
        body {
            padding-bottom: 75px !important; /* Increased slightly for potentially taller icons/text */
            background-color: #f8f9fa; /* Optional: Light grey background for the body */
        }
        .footer {
            border-top: 1px solid #dee2e6; /* Subtle top border */
        }
        .footer .nav-link {
            padding-top: 0.5rem; /* Adjusted padding */
            padding-bottom: 0.5rem;
            color: #6c757d; /* Default icon/text color (grey) */
            text-align: center;
            transition: all 0.2s ease-in-out; /* Smooth transition for color and background */
            border-radius: 8px; /* Add slight rounding to the link area */
            margin: 0 2px; /* Add small horizontal margin */
        }
        .footer .nav-link.active {
            color: #ffffff !important; /* White text for active */
            background-color: var(--bs-primary); /* Use primary color for background */
            /* box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); Optional: Subtle shadow for active */
        }
        .footer .nav-link:not(.active):hover {
            color: #0d6efd; /* Darker grey on hover for non-active items */
            background-color: #e9ecef; /* Light background on hover */
        }
        .footer .nav-link span {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .footer .nav-icon {
            margin-bottom: 0.15rem; /* Adjusted icon margin */
            font-size: 1.2rem; /* Slightly larger icons */
        }
        .footer .nav-text {
            font-size: 0.7rem; /* Slightly smaller text */
            line-height: 1;
            font-weight: 500; /* Slightly bolder text */
        }
    </style>

    <!-- Modal (Keep or remove if not needed by the new 'More' button) -->
    <!-- <div class="modal fade" id="menumodal" tabindex="-1" aria-hidden="true"> ... </div> -->


    <!-- Required jquery and libraries -->
    <script src="assets/js/jquery-3.3.1.min.js"></script>
    <script src="assets/js/popper.min.js"></script>
    <script src="assets/vendor/bootstrap-5/js/bootstrap.bundle.min.js"></script>

    <!-- Customized jquery file  -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/color-scheme.js"></script>



    <!-- Chart js script -->
    <script src="assets/vendor/chart-js-3.3.1/chart.min.js"></script>

    <!-- Progress circle js script -->
    <script src="assets/vendor/progressbar-js/progressbar.min.js"></script>

    <!-- swiper js script -->
    <script src="assets/vendor/swiperjs-6.6.2/swiper-bundle.min.js"></script>

    <!-- page level custom script -->
    <script src="assets/js/app.js"></script>

    <!-- daterange picker script -->
    <!-- Moment.js is usually needed for daterangepicker -->
    <script src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script> 
    <script src="assets/vendor/daterangepicker/daterangepicker.js"></script>

    <!-- nouislider JS -->
    <!-- <script src="assets/vendor/nouislider/nouislider.min.js"></script> --> <!-- Make sure nouislider JS is included if needed -->

    <!-- Removed duplicate includes below -->
    <!-- <script src="lazy-dev/assets/js/core/bootstrap.min.js"></script> -->



</body>

</html>